-- ExtractAtlasInfos - Atlas纹理信息提取插件
-- 适用于魔兽世界 3.3.5 版本

-- 插件命名空间
ExtractAtlasInfos = {}

-- 本地化变量，提高性能
local _G = _G
local pairs = pairs
local type = type
local print = print

-- 本地函数声明
local PrintTextureInfo



-- 插件初始化
function ExtractAtlasInfos:OnLoad()
    -- 注册事件
    self:RegisterEvent("ADDON_LOADED")
    
    -- 输出加载信息
    print("|cff00ff00ExtractAtlasInfos|r: 插件已加载")
end

-- 事件处理
function ExtractAtlasInfos:OnEvent(event, ...)
    if event == "ADDON_LOADED" then
        local addonName = ...
        if addonName == "ExtractAtlasInfos" then
            -- 插件加载完成后的初始化
            self:Initialize()
        end
    end
end

-- 插件初始化函数
function ExtractAtlasInfos:Initialize()
    -- 检查 AtlasInfo 全局变量是否已加载
    if _G.AtlasInfo then
        print("|cff00ff00ExtractAtlasInfos|r: 初始化完成，Atlas数据已就绪")

        -- 测试功能：打印一个示例纹理信息
        -- PrintTextureInfo("AdventureMapLabel-Large")

        -- ShowAtlasTexture("legioninvasion-scenario-rewardring")

        -- 添加一个简单的统计信息
        local textureCount = 0
        for _, textureData in pairs(_G.AtlasInfo) do
            if type(textureData) == "table" then
                for _ in pairs(textureData) do
                    textureCount = textureCount + 1
                end
            end
        end
        print("|cff00ff00ExtractAtlasInfos|r: 共加载了 " .. textureCount .. " 个纹理信息")
    else
        print("|cffff0000ExtractAtlasInfos|r: 错误 - AtlasInfo 数据未找到")
        return
    end
end

-- 主要功能函数：通过纹理名称获取Atlas信息
-- @param textureName string 纹理名称（如 "AdventureMapLabel-Large"）
-- @return table|nil 返回包含Atlas路径和纹理数据的表，如果未找到则返回nil
function GetAtlasTextureInfo(textureName)
    -- 参数验证
    if not textureName or type(textureName) ~= "string" or textureName == "" then
        print("|cffff0000ExtractAtlasInfos|r: 错误 - 无效的纹理名称")
        return nil
    end
    
    -- 检查 AtlasInfo 是否存在
    if not _G.AtlasInfo then
        print("|cffff0000ExtractAtlasInfos|r: 错误 - AtlasInfo 数据未加载")
        return nil
    end

    -- 遍历所有Atlas文件查找指定纹理
    for atlasPath, textureData in pairs(_G.AtlasInfo) do
        if type(textureData) == "table" then
            -- 在当前Atlas文件中查找纹理
            for currentTextureName, textureInfo in pairs(textureData) do
                if currentTextureName == textureName then
                    -- 找到匹配的纹理，返回完整信息
                    -- 将反斜杠替换为正斜杠以符合魔兽世界客户端要求
                    local normalizedAtlasPath = string.gsub(atlasPath, "/", "\\")
                    -- print("atlasPath : ".. atlasPath)
                    -- print("normalizedAtlasPath : ".. normalizedAtlasPath)
                    return {
                        atlasPath = normalizedAtlasPath,
                        textureName = textureName,
                        textureData = textureInfo,
                        -- 解析纹理数据（便于使用）
                        width = textureInfo[1],
                        height = textureInfo[2],
                        left = textureInfo[3],
                        right = textureInfo[4],
                        top = textureInfo[5],
                        bottom = textureInfo[6],
                        flipHorizontal = textureInfo[7],
                        flipVertical = textureInfo[8]
                    }
                end
            end
        end
    end
    
    -- 未找到指定纹理
    return nil
end

-- 辅助函数：获取所有可用的纹理名称列表
-- @return table 包含所有纹理名称的数组
function GetAllTextureNames()
    local textureNames = {}

    if not _G.AtlasInfo then
        return textureNames
    end

    for _, textureData in pairs(_G.AtlasInfo) do
        if type(textureData) == "table" then
            for textureName, _ in pairs(textureData) do
                table.insert(textureNames, textureName)
            end
        end
    end
    
    return textureNames
end

-- 辅助函数：根据Atlas路径获取该Atlas中的所有纹理
-- @param atlasPath string Atlas文件路径
-- @return table|nil 包含该Atlas中所有纹理信息的表
function GetTexturesByAtlasPath(atlasPath)
    if not atlasPath or not _G.AtlasInfo then
        return nil
    end

    return _G.AtlasInfo[atlasPath]
end

-- 辅助函数：搜索包含指定关键词的纹理名称
-- @param keyword string 搜索关键词
-- @return table 包含匹配纹理信息的数组
function SearchTexturesByKeyword(keyword)
    local results = {}

    if not keyword or not _G.AtlasInfo then
        return results
    end

    keyword = string.lower(keyword)

    for atlasPath, textureData in pairs(_G.AtlasInfo) do
        if type(textureData) == "table" then
            for textureName, textureInfo in pairs(textureData) do
                if string.find(string.lower(textureName), keyword) then
                    -- 将反斜杠替换为正斜杠以符合魔兽世界客户端要求
                    local normalizedAtlasPath = string.gsub(atlasPath, "/", "\\")
                    table.insert(results, {
                        atlasPath = normalizedAtlasPath,
                        textureName = textureName,
                        textureData = textureInfo
                    })
                end
            end
        end
    end
    
    return results
end

-- 调试函数：打印纹理信息（本地函数）
-- @param textureName string 纹理名称
PrintTextureInfo = function(textureName)
    local info = GetAtlasTextureInfo(textureName)
    
    if info then
        print("|cff00ff00ExtractAtlasInfos|r: 纹理信息 - " .. textureName)
        print("  Atlas路径: " .. info.atlasPath)
        print("  尺寸: " .. info.width .. "x" .. info.height)
        print("  坐标: left=" .. info.left .. ", right=" .. info.right .. ", top=" .. info.top .. ", bottom=" .. info.bottom)
        print("  翻转: 水平=" .. tostring(info.flipHorizontal) .. ", 垂直=" .. tostring(info.flipVertical))
    else
        print("|cffff0000ExtractAtlasInfos|r: 未找到纹理 '" .. textureName .. "'")
    end
end

-- 创建框架用于事件处理
local frame = CreateFrame("Frame")
frame:SetScript("OnEvent", function(_, event, ...)
    ExtractAtlasInfos:OnEvent(event, ...)
end)

-- 注册事件
frame:RegisterEvent("ADDON_LOADED")


-- 显示Atlas纹理的函数
-- @param textureName string 纹理名称（如 "AdventureMapLabel-Large"）
-- @return Frame|nil 返回创建的框架对象，如果失败则返回nil
function ShowAtlasTexture(textureName)
    -- 参数验证
    if not textureName or type(textureName) ~= "string" or textureName == "" then
        print("|cffff0000ExtractAtlasInfos|r: ShowAtlasTexture - 无效的纹理名称")
        return nil
    end

    -- 获取Atlas纹理信息
    local textureInfo = GetAtlasTextureInfo(textureName)
    if not textureInfo then
        print("|cffff0000ExtractAtlasInfos|r: ShowAtlasTexture - 未找到纹理 '" .. textureName .. "'")
        return nil
    end

    -- 创建本地框架作为容器
    local frame = CreateFrame("Frame", nil, UIParent)
    frame:SetSize(200, 200) -- 设置框架大小为200x200像素
    frame:SetPoint("CENTER") -- 居中显示
    frame:Show()         -- 使框架可见
    frame:EnableMouse(true) -- 使框架可交互

    local texture = frame:CreateTexture()
    texture:SetPoint("CENTER")     -- 设置纹理的锚点和相对位置

    -- 应用纹理设置
    texture:SetTexture(textureInfo.atlasPath)  -- 设置纹理路径
    texture:SetSize(textureInfo.width, textureInfo.height)  -- 设置纹理尺寸

    -- 设置纹理坐标（UV坐标）
    texture:SetTexCoord(
        textureInfo.left,    -- 左边界
        textureInfo.right,   -- 右边界
        textureInfo.top,     -- 上边界
        textureInfo.bottom   -- 下边界
    )

    -- 添加关闭按钮（可选功能）
    -- local closeButton = CreateFrame("Button", nil, frame, "UIPanelCloseButton")
    -- closeButton:SetPoint("TOPRIGHT", frame, "TOPRIGHT", 0, 0)
    -- closeButton:SetScript("OnClick", function()
    --     frame:Hide()
    -- end)

    -- 添加标题文本显示纹理名称
    local titleText = frame:CreateFontString(nil, "OVERLAY", "SystemFont_OutlineThick_Huge2")
    titleText:SetPoint("TOP", frame, "TOP", 0, -5)
    titleText:SetText(textureName)
    -- titleText:SetTextColor(1, 1, 1, 1)  -- 白色文字

    -- 输出成功信息
    print("|cff00ff00ExtractAtlasInfos|r: 已显示纹理 '" .. textureName .. "' (尺寸: " .. textureInfo.width .. "x" .. textureInfo.height .. ")")

    -- 返回创建的框架对象
    return frame
end


-- 插件加载时初始化
ExtractAtlasInfos:OnLoad()





