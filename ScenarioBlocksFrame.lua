-- ScenarioBlocksFrame.lua
-- 将temp1.xml中的所有XML内容转换为符合World of Warcraft 3.3.5 API规范的Lua代码

-- 调试模式控制开关
local DEBUG_ENABLED = true
local OBJECTIVES_COUNT = 6
local OBJECTIVES_TABLE = {} -- 演示用目标数据
local ICON_NAME_TABLE = { "Objective-Nub","Tracker-Check","Objective-Fail" } -- 演示用图标数据
-- 全局测试框架变量
local TheScenarioFrame = nil

-- 统一调试函数
local function DebugPrint(message)
    if DEBUG_ENABLED then
        local currentTime = time()
        local gameTime = GetTime()
        local timeTable = date("*t", currentTime)
        local hours = timeTable.hour
        local minutes = timeTable.min
        local seconds = timeTable.sec
        local milliseconds = math.floor((gameTime - math.floor(gameTime)) * 1000)
        local timestamp = string.format("|cff888888[%02d:%02d:%02d.%03d]|r",
                                      hours, minutes, seconds, milliseconds)
        print(timestamp .. " " .. message)
    end
end

local function InitObjectivesTable(text, iconIndex)
    if iconIndex > #ICON_NAME_TABLE then
        DebugPrint("|cffff0000ScenarioBlocks|r: InitObjectivesTable - iconnum超出范围: " .. iconIndex)
        return
    end 
    local icon = ICON_NAME_TABLE[iconIndex]
    OBJECTIVES_TABLE[#OBJECTIVES_TABLE+1] = {text = text, icon = icon}
end

ScenarioBlocksFrameManager = {}

-- 创建Atlas纹理的辅助函数 - 参考ChallengesKeystoneFrameUI.lua
function ScenarioBlocksFrameManager:CreateAtlasTexture(parent, atlasName, layer, sublevel, useAtlasSize)
    local texture = parent:CreateTexture(nil, layer or "ARTWORK", nil, sublevel or 0)

    -- 获取Atlas纹理信息
    local atlasInfo = GetAtlasTextureInfo(atlasName)
    if atlasInfo then
        texture:SetTexture(atlasInfo.atlasPath)
        texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)

        if useAtlasSize then
            texture:SetSize(atlasInfo.width, atlasInfo.height)
        end
        DebugPrint("|cff00ff00ScenarioBlocks|r: 成功加载Atlas纹理 '" .. atlasName .. "'")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法找到Atlas纹理: " .. atlasName)
        -- 设置一个默认纹理或颜色作为后备
        --texture:SetColorTexture(1, 1, 1, 0.5) -- 白色半透明作为占位符
    end

    return texture
end

-- 主函数：创建ScenarioBlocksFrame及其所有子框架（使用固定高度Frame替代ScrollFrame）
-- @param parentFrame Frame 父框架（可以是ObjectiveTrackerBlocksFrame或其他）
-- @param frameName string 框架名称（可选）
-- @param blockTypes table 要创建的子框架类型数组（可选），可选值：{"ObjectiveBlock", "StageBlock", "ChallengeModeBlock", "ProvingGroundsBlock"}
-- @return Frame 创建的主框架对象
--
-- 使用示例：
-- 1. 创建所有子框架（默认行为）：CreateScenarioBlocksFrame(parent, "MyFrame")
-- 2. 只创建特定子框架：CreateScenarioBlocksFrame(parent, "MyFrame", {"StageBlock", "ProvingGroundsBlock"})
-- 3. 创建单个子框架：CreateScenarioBlocksFrame(parent, "MyFrame", {"ChallengeModeBlock"})
function ScenarioBlocksFrameManager:CreateScenarioBlocksFrame(parentFrame, frameName, blockTypes)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建ScenarioBlocksFrame（固定高度Frame方案）")

    local name = frameName or "ScenarioBlocksFrame"

    -- 创建主容器框架 - 使用普通Frame替代ScrollFrame
    local mainFrame = CreateFrame("Frame", name, parentFrame)
    if parentFrame ~= UIParent then
        mainFrame:SetPoint("BOTTOMLEFT", parentFrame, "BOTTOMLEFT", 0, 0)
    end

    mainFrame:SetSize(261, 87) -- 固定尺寸
    mainFrame:Show()
    parentFrame.MainFrame = mainFrame

    DebugPrint("|cff00ff00ScenarioBlocks|r: 主容器框架已创建")

    -- 前一个框架
    local previousFrame = mainFrame

    -- 处理子框架类型参数
    local shouldCreateBlock = {}
    if blockTypes and type(blockTypes) == "table" and #blockTypes > 0 then
        -- 使用指定的子框架类型
        for _, blockType in ipairs(blockTypes) do
            shouldCreateBlock[blockType] = true
        end
        DebugPrint("|cff00ff00ScenarioBlocks|r: 将创建指定的子框架类型: " .. table.concat(blockTypes, ", "))
    else
        -- 默认行为：创建所有子框架（根据当前注释状态）
        shouldCreateBlock["ObjectiveBlock"] = false  -- 当前被注释
        shouldCreateBlock["StageBlock"] = false      -- 当前被注释
        shouldCreateBlock["ChallengeModeBlock"] = false -- 当前被注释
        shouldCreateBlock["ProvingGroundsBlock"] = false -- 当前被注释
        DebugPrint("|cff00ff00ScenarioBlocks|r: 使用默认子框架创建行为")
    end

    -- 根据配置创建子框架
    if shouldCreateBlock["ObjectiveBlock"] then
        previousFrame = self:CreateScenarioObjectiveBlock(previousFrame)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 已创建ObjectiveBlock")
    end

    if shouldCreateBlock["StageBlock"] then
        previousFrame = self:CreateScenarioStageBlock(previousFrame)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 已创建StageBlock")
    end

    if shouldCreateBlock["ChallengeModeBlock"] then
        previousFrame = self:CreateScenarioChallengeModeBlock(previousFrame)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 已创建ChallengeModeBlock")
    end

    if shouldCreateBlock["ProvingGroundsBlock"] then
        self:CreateScenarioProvingGroundsBlock(previousFrame)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 已创建ProvingGroundsBlock")
    end

    -- 设置脚本事件
    -- self:SetupScenarioBlocksFrameScripts(mainFrame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: ScenarioBlocksFrame创建完成（固定高度Frame方案）")
    return mainFrame
end

-- 中文子框架类型名称转换为英文blockType的辅助函数
-- @param chineseName string 中文的子框架类型名称
-- @return string|nil 对应的英文blockType字符串，匹配失败时返回nil
--
-- 支持的中文名称映射：
-- "目标块" 或 "目标框架" → "ObjectiveBlock"
-- "阶段块" 或 "阶段框架" → "StageBlock"
-- "挑战模式块" 或 "挑战模式框架" → "ChallengeModeBlock"
-- "试炼场块" 或 "试炼场框架" → "ProvingGroundsBlock"
--
-- 使用示例：
-- local blockType = ScenarioBlocksFrameManager:GetBlockTypeFromChineseName("阶段块")
-- -- 返回 "StageBlock"
function ScenarioBlocksFrameManager:GetBlockTypeFromChineseName(chineseName)
    if not chineseName or type(chineseName) ~= "string" then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetBlockTypeFromChineseName 参数无效: " .. tostring(chineseName))
        return nil
    end

    -- 转换为小写以支持大小写不敏感匹配
    local lowerName = string.lower(chineseName)

    -- 中英文映射表
    local chineseToEnglishMap = {
        -- 目标相关
        ["目标块"] = "ObjectiveBlock",
        ["目标框架"] = "ObjectiveBlock",
        ["目标追踪块"] = "ObjectiveBlock",
        ["目标追踪框架"] = "ObjectiveBlock",

        -- 阶段相关
        ["阶段块"] = "StageBlock",
        ["阶段框架"] = "StageBlock",
        ["关卡块"] = "StageBlock",
        ["关卡框架"] = "StageBlock",
        ["场景阶段块"] = "StageBlock",
        ["场景阶段框架"] = "StageBlock",

        -- 挑战模式相关
        ["挑战模式块"] = "ChallengeModeBlock",
        ["挑战模式框架"] = "ChallengeModeBlock",
        ["挑战块"] = "ChallengeModeBlock",
        ["挑战框架"] = "ChallengeModeBlock",
        ["史诗钥石块"] = "ChallengeModeBlock",
        ["史诗钥石框架"] = "ChallengeModeBlock",
        ["大秘境块"] = "ChallengeModeBlock",
        ["大秘境框架"] = "ChallengeModeBlock",

        -- 试炼场相关
        ["试炼场块"] = "ProvingGroundsBlock",
        ["试炼场框架"] = "ProvingGroundsBlock",
        ["试炼块"] = "ProvingGroundsBlock",
        ["试炼框架"] = "ProvingGroundsBlock",
        ["竞技场块"] = "ProvingGroundsBlock",
        ["竞技场框架"] = "ProvingGroundsBlock"
    }

    -- 查找匹配的英文类型
    local englishType = chineseToEnglishMap[lowerName]

    if englishType then
        DebugPrint("|cff00ff00ScenarioBlocks|r: 中文名称 '" .. chineseName .. "' 转换为英文类型: " .. englishType)
        return englishType
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 未找到匹配的中文名称: " .. chineseName)
        return nil
    end
end

-- 批量转换中文子框架类型名称数组为英文blockTypes数组的辅助函数
-- @param chineseNames table 中文子框架类型名称数组
-- @return table|nil 转换后的英文blockTypes数组，如果输入无效或转换失败则返回nil
--
-- 使用示例：
-- local blockTypes = ScenarioBlocksFrameManager:ConvertChineseNamesToBlockTypes({"阶段块", "试炼场框架"})
-- -- 返回 {"StageBlock", "ProvingGroundsBlock"}
function ScenarioBlocksFrameManager:ConvertChineseNamesToBlockTypes(chineseNames)
    if not chineseNames or type(chineseNames) ~= "table" or #chineseNames == 0 then
        DebugPrint("|cffff0000ScenarioBlocks|r: ConvertChineseNamesToBlockTypes 参数无效")
        return nil
    end

    local blockTypes = {}
    local convertedCount = 0

    for _, chineseName in ipairs(chineseNames) do
        local englishType = self:GetBlockTypeFromChineseName(chineseName)
        if englishType then
            table.insert(blockTypes, englishType)
            convertedCount = convertedCount + 1
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: 跳过无效的中文名称: " .. tostring(chineseName))
        end
    end

    if convertedCount > 0 then
        DebugPrint("|cff00ff00ScenarioBlocks|r: 成功转换 " .. convertedCount .. " 个中文名称为英文类型")
        return blockTypes
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 没有成功转换任何中文名称")
        return nil
    end
end

-- 创建ScenarioObjectiveBlock框架
function ScenarioBlocksFrameManager:CreateScenarioObjectiveBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioObjectiveBlock")

    local frame = CreateFrame("Frame", "ScenarioObjectiveBlock", parent)
    frame:SetPoint("LEFT", parent, "LEFT", 0, 0)
    frame:SetSize(192, 10) -- 对应XML中的Size x="192" y="10"
    frame:Show() -- 对应XML中的hidden="true"

    parent.ScenarioObjectiveBlock = frame
    return frame
end

-- 创建ScenarioStageBlock框架
function ScenarioBlocksFrameManager:CreateScenarioStageBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioStageBlock")

    local frame = CreateFrame("Frame", "ScenarioStageBlock", parent)
    frame:SetPoint("LEFT", parent, "LEFT", 0, 0)
    frame:SetSize(201, 83) -- 对应XML中的Size x="201" y="83"
    frame:Show() -- 对应XML中的hidden="true"

    -- 创建层级结构
    self:CreateScenarioStageBlockLayers(frame)
    --self:CreateScenarioStageBlockFrames(frame)
    --self:SetupScenarioStageBlockScripts(frame)

    self:CreateDemoObjectiveLines(frame, OBJECTIVES_COUNT, OBJECTIVES_TABLE)

    parent.ScenarioStageBlock = frame
    return frame
end

-- 创建ScenarioStageBlock的层级结构
function ScenarioBlocksFrameManager:CreateScenarioStageBlockLayers(frame)
    -- BACKGROUND层
    local normalBG = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BACKGROUND", 0, true)
    normalBG:SetPoint("TOPLEFT", frame, "TOPLEFT", 0, 0)
    frame.NormalBG = normalBG -- 对应XML中的parentKey="NormalBG"

    -- BORDER层
    local finalBG = self:CreateAtlasTexture(frame, "ScenarioTrackerToast-FinalFiligree", "BORDER", 0, true)
    finalBG:SetPoint("TOPLEFT", frame, "TOPLEFT", 4, -4)
    frame.FinalBG = finalBG -- 对应XML中的parentKey="FinalBG"

    local glowTexture = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BORDER", 0, true)
    glowTexture:SetPoint("TOPLEFT", frame, "TOPLEFT", 0, 0)
    glowTexture:SetAlpha(0) -- 对应XML中的alpha="0"
    glowTexture:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    frame.GlowTexture = glowTexture -- 对应XML中的parentKey="GlowTexture"

    -- 创建GlowTexture的动画
    self:CreateGlowTextureAnimation(glowTexture)

    -- ARTWORK层 - 字体字符串
    self:CreateScenarioStageBlockFontStrings(frame)
end

-- 创建ScenarioStageBlock的字体字符串
function ScenarioBlocksFrameManager:CreateScenarioStageBlockFontStrings(frame)
    -- Stage字体字符串
    local stage = frame:CreateFontString(nil, "ARTWORK", "QuestTitleFont")
    stage:SetSize(172, 18) -- 对应XML中的Size x="172" y="18"
    stage:SetPoint("TOPLEFT", frame, "TOPLEFT", 15, -10)
    stage:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    stage:SetText("阶段: ") -- 对应XML中的text="STAGE"
    stage:SetWordWrap(true) -- 对应XML中的wordwrap="true"
    stage:SetTextColor(1, 0.914, 0.682) -- 对应XML中的Color r="1" g="0.914" b="0.682"
    stage:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    stage:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.Stage = stage -- 对应XML中的parentKey="Stage"

    -- CompleteLabel字体字符串
    local completeLabel = frame:CreateFontString(nil, "ARTWORK", "QuestTitleFont")
    completeLabel:SetPoint("LEFT", frame, "LEFT", 15, 3)
    completeLabel:SetText("") -- 对应XML中的text="STAGE_COMPLETE"
    completeLabel:Show() -- 对应XML中的hidden="true"
    completeLabel:SetTextColor(1, 0.914, 0.682) -- 对应XML中的Color
    completeLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    completeLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.CompleteLabel = completeLabel -- 对应XML中的parentKey="CompleteLabel"

    -- Name字体字符串
    local name = frame:CreateFontString(nil, "ARTWORK", "GameFontNormal")
    name:SetSize(172, 28) -- 对应XML中的Size x="172" y="28"
    name:SetPoint("TOPLEFT", stage, "BOTTOMLEFT", 0, -4) -- 对应XML中的相对定位
    name:SetText("天灾入侵") -- 对应XML中的text="阶段名称"
    name:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    name:SetJustifyV("TOP") -- 对应XML中的justifyV="TOP"
    name:SetSpacing(2) -- 对应XML中的spacing="2"
    name:SetTextColor(1, 0.831, 0.380) -- 对应XML中的Color r="1" g="0.831" b="0.380"
    frame.Name = name -- 对应XML中的parentKey="Name"
end

-- 创建GlowTexture的动画组
function ScenarioBlocksFrameManager:CreateGlowTextureAnimation(glowTexture)
    local animGroup = glowTexture:CreateAnimationGroup()
    glowTexture.AlphaAnim = animGroup -- 对应XML中的parentKey="AlphaAnim"

    -- 第一个Alpha动画 - 从0变为1，使用SetChange(1)
    local alpha1 = animGroup:CreateAnimation("Alpha")
    alpha1:SetChange(1) -- 对应XML中的fromAlpha="0" toAlpha="1"，变化量为1
    alpha1:SetDuration(0.266) -- 对应XML中的duration="0.266"
    alpha1:SetOrder(1) -- 对应XML中的order="1"

    -- 第二个Alpha动画 - 从1变为0，使用SetChange(-1)
    local alpha2 = animGroup:CreateAnimation("Alpha")
    alpha2:SetEndDelay(0.2) -- 对应XML中的endDelay="0.2"
    alpha2:SetChange(-1) -- 对应XML中的fromAlpha="1" toAlpha="0"，变化量为-1
    alpha2:SetDuration(0.333) -- 对应XML中的duration="0.333"
    alpha2:SetOrder(2) -- 对应XML中的order="2"
end

-- 创建ScenarioStageBlock的子框架
function ScenarioBlocksFrameManager:CreateScenarioStageBlockFrames(frame)

    -- 创建RewardButton
    local rewardButton = CreateFrame("Button", nil, frame)
    rewardButton:SetSize(48, 48) -- 对应XML中的Size x="48" y="48"
    rewardButton:SetPoint("BOTTOMRIGHT", frame, "BOTTOMRIGHT", 50, -3)
    rewardButton:Hide() -- 对应XML中的hidden="true"
    frame.RewardButton = rewardButton -- 对应XML中的parentKey="RewardButton"

    -- 创建RewardButton的纹理层
    self:CreateRewardButtonLayers(rewardButton)
    self:SetupRewardButtonScripts(rewardButton)
end

-- 创建RewardButton的纹理层
function ScenarioBlocksFrameManager:CreateRewardButtonLayers(button)
    -- OVERLAY层 textureSubLevel="1" - RewardRing
    local rewardRing = self:CreateAtlasTexture(button, "legioninvasion-scenario-rewardring", "OVERLAY", 1, true)
    rewardRing:SetPoint("CENTER", button, "CENTER") -- 对应XML中的相对定位
    button.RewardRing = rewardRing -- 对应XML中的parentKey="RewardRing"

    -- OVERLAY层 textureSubLevel="0" - RewardIcon
    local rewardIcon = button:CreateTexture(nil, "OVERLAY", nil, 0)
    rewardIcon:SetSize(29, 29) -- 对应XML中的Size x="29" y="29"
    rewardIcon:SetPoint("CENTER", rewardRing, "CENTER") -- 对应XML中的相对定位
    button.RewardIcon = rewardIcon -- 对应XML中的parentKey="RewardIcon"
end

-- 设置RewardButton的脚本事件
function ScenarioBlocksFrameManager:SetupRewardButtonScripts(button)
    -- OnEnter事件
    button:SetScript("OnEnter", function(self)
        if ScenarioRewardButton_OnEnter then
            ScenarioRewardButton_OnEnter(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioRewardButton_OnEnter函数未定义")
        end
    end)

    -- OnLeave事件
    button:SetScript("OnLeave", function(self)
        if ScenarioRewardButton_OnLeave then
            ScenarioRewardButton_OnLeave(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioRewardButton_OnLeave函数未定义")
        end
    end)
end

-- 设置ScenarioStageBlock的脚本事件
function ScenarioBlocksFrameManager:SetupScenarioStageBlockScripts(frame)
    -- OnLoad事件
    frame:SetScript("OnLoad", function(self)
        -- 对应XML中的OnLoad脚本
        if self.Stage then
            -- 设置字体对象尝试列表
            -- 注意：在WoW 3.3.5中可能需要使用不同的方法
            if self.Stage.SetFontObjectsToTry then
                self.Stage:SetFontObjectsToTry("QuestTitleFont", "Fancy16Font", "SystemFont_Med1")
            else
                -- 备用方法：直接设置字体对象
                self.Stage:SetFontObject("QuestTitleFont")
            end
        end
    end)

    -- OnEnter事件
    frame:SetScript("OnEnter", function(self)
        if ScenarioObjectiveStageBlock_OnEnter then
            ScenarioObjectiveStageBlock_OnEnter(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioObjectiveStageBlock_OnEnter函数未定义")
        end
    end)

    -- OnLeave事件
    frame:SetScript("OnLeave", function(self)
        GameTooltip:Hide() -- 对应XML中的OnLeave脚本
    end)
end

-- 创建ScenarioChallengeModeBlock框架
function ScenarioBlocksFrameManager:CreateScenarioChallengeModeBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioChallengeModeBlock")

    local frame = CreateFrame("Frame", "ScenarioChallengeModeBlock", parent)
    frame:SetPoint("LEFT", 0, 0)
    frame:SetSize(261, 87) -- 对应XML中的Size x="251" y="87"
    frame:Show() -- 对应XML中的hidden="true"

    -- 创建层级结构
    self:CreateChallengeModeBlockLayers(frame)
    self:CreateChallengeModeBlockFrames(frame)

    self:CreateDemoObjectiveLines(frame, OBJECTIVES_COUNT, OBJECTIVES_TABLE)
    
    parent.ScenarioChallengeModeBlock = frame
    return frame
end

-- 创建ScenarioChallengeModeBlock的层级结构
function ScenarioBlocksFrameManager:CreateChallengeModeBlockLayers(frame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioChallengeModeBlock的层级结构")
    -- BACKGROUND层
    local timerBGBack = self:CreateAtlasTexture(frame, "ChallengeMode-TimerBG-back", "BACKGROUND", 0, true)
    timerBGBack:SetPoint("BOTTOM", frame, 0, 13)
    frame.TimerBGBack = timerBGBack -- 对应XML中的parentKey="TimerBGBack"

    -- BACKGROUND层 textureSubLevel="1"
    local timerBG = self:CreateAtlasTexture(frame, "ChallengeMode-TimerBG", "BACKGROUND", 1, true)
    timerBG:SetPoint("BOTTOM", frame, 0, 13)
    frame.TimerBG = timerBG -- 对应XML中的parentKey="TimerBG"

    -- OVERLAY层
    local overlayTexture = self:CreateAtlasTexture(frame, "ChallengeMode-Timer", "OVERLAY", 0, false)
    overlayTexture:SetAllPoints(frame) -- 对应XML中的setAllPoints="true"
    frame.OverlayTexture = overlayTexture -- 对应XML中的parentKey="OverlayTexture"


    -- OVERLAY层字体字符串
    local level = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalMed2")
    level:SetPoint("TOPLEFT", frame, "TOPLEFT", 28, -18)
    level:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    level:SetText("LEVEL 10") -- 对应XML中的text="LEVEL"
    frame.Level = level -- 对应XML中的parentKey="Level"

    local timeLeft = frame:CreateFontString(nil, "OVERLAY", "GameFontHighlightHuge")
    timeLeft:SetPoint("TOPLEFT", level, "BOTTOMLEFT", 0, -8) -- 对应XML中的相对定位
    timeLeft:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    timeLeft:SetText("40:00") -- 对应XML中的text="TIME LEFT"
    frame.TimeLeft = timeLeft -- 对应XML中的parentKey="TimeLeft"

    -- 初始化倒计时功能
    self:InitializeCountdownTimer(frame)
end

-- 初始化挑战模式倒计时器
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:InitializeCountdownTimer(frame)
    if not frame.TimeLeft then
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法初始化倒计时，TimeLeft字体字符串不存在")
        return
    end

    -- 倒计时状态数据
    frame.countdownData = {
        totalSeconds = 40 * 60,  -- 默认40分钟（2400秒）
        remainingSeconds = 40 * 60,
        isRunning = false,
        isPaused = false,
        lastUpdateTime = 0,
        warningThreshold = 5 * 60,  -- 5分钟警告阈值
        originalColor = {1, 1, 1},  -- 原始白色
        warningColor = {1, 0.2, 0.2}  -- 警告红色
    }

    -- 创建更新定时器框架
    frame.countdownTimer = CreateFrame("Frame")
    frame.countdownTimer.parentFrame = frame

    -- 设置OnUpdate事件处理器
    frame.countdownTimer:SetScript("OnUpdate", function(self, elapsed)
        ScenarioBlocksFrameManager:UpdateCountdownTimer(self.parentFrame, elapsed)
    end)
    self:StartCountdown(frame, 1*60)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时器已初始化，默认时间: " ..
              ScenarioBlocksFrameManager:FormatTime(frame.countdownData.totalSeconds))
end

-- 更新倒计时显示
-- @param frame Frame 挑战模式框架
-- @param elapsed number 自上次更新以来的时间（秒）
function ScenarioBlocksFrameManager:UpdateCountdownTimer(frame, elapsed)
    local data = frame.countdownData
    if not data or not data.isRunning or data.isPaused then
        return
    end

    -- 累积时间
    data.lastUpdateTime = data.lastUpdateTime + elapsed

    -- 每秒更新一次
    if data.lastUpdateTime >= 1.0 then
        data.remainingSeconds = data.remainingSeconds - math.floor(data.lastUpdateTime)
        data.lastUpdateTime = data.lastUpdateTime - math.floor(data.lastUpdateTime)

        -- 确保不会变成负数
        if data.remainingSeconds < 0 then
            data.remainingSeconds = 0
            data.isRunning = false
            DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时结束")
        end

        -- 更新显示
        self:UpdateCountdownDisplay(frame)

        -- 更新进度条
        self:UpdateCountdownProgressBar(frame)

        -- 检查是否需要颜色警告
        self:CheckCountdownWarning(frame)
    end
end

-- 更新倒计时显示文本
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:UpdateCountdownDisplay(frame)
    if not frame.TimeLeft or not frame.countdownData then
        return
    end

    local timeText = self:FormatTime(frame.countdownData.remainingSeconds)
    frame.TimeLeft:SetText(timeText)
end

-- 检查倒计时警告状态
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:CheckCountdownWarning(frame)
    local data = frame.countdownData
    if not data or not frame.TimeLeft then
        return
    end

    -- 如果剩余时间少于警告阈值，改变颜色为红色
    if data.remainingSeconds <= data.warningThreshold and data.remainingSeconds > 0 then
        frame.TimeLeft:SetTextColor(data.warningColor[1], data.warningColor[2], data.warningColor[3])
    else
        frame.TimeLeft:SetTextColor(data.originalColor[1], data.originalColor[2], data.originalColor[3])
    end
end

-- 更新倒计时进度条
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:UpdateCountdownProgressBar(frame)
    local data = frame.countdownData
    local statusBar = frame.StatusBar

    if not data or not statusBar or not statusBar.countdownLinked then
        return
    end

    -- 计算进度百分比
    local progressPercent = 0
    if data.totalSeconds > 0 then
        progressPercent = data.remainingSeconds / data.totalSeconds
    end

    -- 确保百分比在0-1范围内
    progressPercent = math.max(0, math.min(1, progressPercent))

    -- 计算进度条值（0-1000）
    local progressValue = progressPercent * 1000
    statusBar:SetValue(progressValue)

    -- 根据剩余时间百分比调整进度条颜色
    if progressPercent <= statusBar.warningThreshold then
        -- 时间不足时显示警告色（红色）
        statusBar:SetStatusBarColor(statusBar.warningColor[1], statusBar.warningColor[2], statusBar.warningColor[3])
    else
        -- 正常时间显示原始色（蓝色）
        statusBar:SetStatusBarColor(statusBar.originalColor[1], statusBar.originalColor[2], statusBar.originalColor[3])
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 进度条已更新 - 百分比: " ..
              string.format("%.1f%%", progressPercent * 100) ..
              ", 值: " .. math.floor(progressValue))
end

-- 格式化时间为MM:SS格式
-- @param seconds number 总秒数
-- @return string 格式化的时间字符串
function ScenarioBlocksFrameManager:FormatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local secs = seconds % 60
    return string.format("%02d:%02d", minutes, secs)
end

-- 启动倒计时
-- @param frame Frame 挑战模式框架
-- @param initialSeconds number 初始秒数（可选，默认使用当前设置）
function ScenarioBlocksFrameManager:StartCountdown(frame, initialSeconds)
    if not frame.countdownData then
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法启动倒计时，倒计时数据未初始化")
        return
    end

    local data = frame.countdownData

    if initialSeconds then
        data.totalSeconds = initialSeconds
        data.remainingSeconds = initialSeconds
    end

    data.isRunning = true
    data.isPaused = false
    data.lastUpdateTime = 0

    -- 重置颜色
    frame.TimeLeft:SetTextColor(data.originalColor[1], data.originalColor[2], data.originalColor[3])

    -- 更新初始显示
    self:UpdateCountdownDisplay(frame)

    -- 更新进度条到满值
    self:UpdateCountdownProgressBar(frame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已启动，时间: " .. self:FormatTime(data.remainingSeconds))
end

-- 暂停倒计时
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:PauseCountdown(frame)
    if not frame.countdownData then
        return
    end

    frame.countdownData.isPaused = true
    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已暂停")
end

-- 恢复倒计时
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:ResumeCountdown(frame)
    if not frame.countdownData then
        return
    end

    frame.countdownData.isPaused = false
    frame.countdownData.lastUpdateTime = 0
    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已恢复")
end

-- 停止倒计时
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:StopCountdown(frame)
    if not frame.countdownData then
        return
    end

    frame.countdownData.isRunning = false
    frame.countdownData.isPaused = false
    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已停止")
end

-- 重置倒计时
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:ResetCountdown(frame)
    if not frame.countdownData then
        return
    end

    local data = frame.countdownData
    data.remainingSeconds = data.totalSeconds
    data.isRunning = false
    data.isPaused = false
    data.lastUpdateTime = 0

    -- 重置颜色和显示
    frame.TimeLeft:SetTextColor(data.originalColor[1], data.originalColor[2], data.originalColor[3])
    self:UpdateCountdownDisplay(frame)

    -- 重置进度条到满值
    self:UpdateCountdownProgressBar(frame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已重置到: " .. self:FormatTime(data.remainingSeconds))
end

-- 创建ScenarioChallengeModeBlock的子框架
function ScenarioBlocksFrameManager:CreateChallengeModeBlockFrames(frame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioChallengeModeBlock的子框架")
    -- 创建StartedDepleted框架
    self:CreateStartedDepletedFrame(frame)

    -- 创建TimesUpLootStatus框架
    self:CreateTimesUpLootStatusFrame(frame)

    -- 创建DeathCount框架
    self:CreateDeathCountFrame(frame)

    -- 创建StatusBar
    self:CreateChallengeModeStatusBar(frame)

    -- 创建词缀框架（继承ScenarioChallengeModeAffixTemplate）
    -- 注意：这里需要根据实际需求创建词缀框架
    -- local affixFrame = CreateFrame("Frame", nil, frame, "ScenarioChallengeModeAffixTemplate")
    -- affixFrame:Hide() -- 对应XML中的hidden="true"
end

-- 创建StartedDepleted框架
function ScenarioBlocksFrameManager:CreateStartedDepletedFrame(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建StartedDepleted框架")
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(19, 20) -- 对应XML中的Size x="19" y="20"
    frame:SetPoint("LEFT", parent.Level, "RIGHT", 4, 0) -- 对应XML中的相对定位
    frame:Hide() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.StartedDepleted = frame -- 对应XML中的parentKey="StartedDepleted"

    -- ARTWORK层
    local chestTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-chest", "ARTWORK", 0, true)
    chestTexture:SetPoint("CENTER", frame)

    -- ARTWORK层 textureSubLevel="1"
    local redlineTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-redline", "ARTWORK", 1, true)
    redlineTexture:SetPoint("CENTER", frame)

    -- 设置脚本事件
    frame:SetScript("OnEnter", function(self)
        GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
        GameTooltip:SetText("枯竭的钥石", 1, 1, 1) -- 对应XML中的文本
        GameTooltip:AddLine("你在完成此地下城后将无法获得战利品宝箱。但在限定时间内完成地下城能够为钥石充能并将其升级。", nil, nil, nil, true) -- 对应XML中的文本
        GameTooltip:Show()
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建TimesUpLootStatus框架
function ScenarioBlocksFrameManager:CreateTimesUpLootStatusFrame(parent)
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(19, 20) -- 对应XML中的Size x="19" y="20"
    frame:SetPoint("LEFT", parent.TimeLeft, "RIGHT", 4, 0) -- 对应XML中的相对定位
    frame:Hide() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.TimesUpLootStatus = frame -- 对应XML中的parentKey="TimesUpLootStatus"

    -- ARTWORK层
    local chestTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-chest", "ARTWORK", 0, true)
    chestTexture:SetPoint("CENTER", frame, "CENTER")

    -- ARTWORK层 textureSubLevel="1"
    local noLootTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-redline", "ARTWORK", 1, true)
    noLootTexture:SetPoint("CENTER", frame, "CENTER")
    frame.NoLoot = noLootTexture -- 对应XML中的parentKey="NoLoot"

    -- 设置脚本事件
    frame:SetScript("OnEnter", function(self)
        ScenarioBlocksFrameManager:Scenario_ChallengeMode_TimesUpLootStatus_OnEnter(self)
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建DeathCount框架
function ScenarioBlocksFrameManager:CreateDeathCountFrame(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建DeathCount框架")
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(20, 20) -- 对应XML中的Size x="20" y="16"
    frame:SetPoint("TOPLEFT", parent, "BOTTOMRIGHT", -47, 43) -- 对应XML中的相对定位
    frame:Show() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.DeathCount = frame -- 对应XML中的parentKey="DeathCount"

    -- 注意：XML中有mixin="ScenarioChallengeDeathCountMixin"，这里需要手动实现相关功能

    -- ARTWORK层
    local icon = self:CreateAtlasTexture(frame, "poi-graveyard-neutral", "ARTWORK", 0, true)
    icon:SetPoint("LEFT", frame, "LEFT")
    frame.Icon = icon -- 对应XML中的parentKey="Icon"

    local count = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightSmall2")
    count:SetPoint("LEFT", icon, "RIGHT", 1, 0) -- 对应XML中的相对定位
    count:SetText("0") -- 对应XML中的text="0"
    frame.Count = count -- 对应XML中的parentKey="Count"

    -- 设置脚本事件（对应XML中的mixin方法）
    frame:SetScript("OnLoad", function(self)
        -- 对应XML中的OnLoad method="OnLoad"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnLoad")
    end)

    frame:SetScript("OnEvent", function(self, event, ...)
        -- 对应XML中的OnEvent method="OnEvent"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnEvent: " .. event)
    end)

    frame:SetScript("OnEnter", function(self)
        -- 对应XML中的OnEnter method="OnEnter"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnEnter")
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建挑战模式状态进度条（与倒计时联动）
function ScenarioBlocksFrameManager:CreateChallengeModeStatusBar(parent)
    local statusBar = CreateFrame("StatusBar", "ScenarioChallengeModeStatusBar", parent)
    statusBar:SetSize(207, 10) -- 对应XML中的Size x="207" y="13"
    statusBar:SetPoint("BOTTOM", parent, 0, 13)
    statusBar:SetFrameLevel(parent:GetFrameLevel())
    parent.StatusBar = statusBar -- 对应XML中的parentKey="StatusBar"

    -- 设置状态栏纹理
    local barTexture = self:CreateAtlasTexture(statusBar, "ChallengeMode-TimerFill", "ARTWORK", 0, false)
    statusBar:SetStatusBarTexture(barTexture) -- 对应XML中的BarTexture atlas="ChallengeMode-TimerFill"

    -- 设置进度条数值范围（0-1000便于精确计算）
    statusBar:SetMinMaxValues(0, 1000)
    statusBar:SetValue(1000) -- 初始满值

    -- 初始化进度条联动数据
    statusBar.countdownLinked = true -- 标记为与倒计时联动
    -- statusBar.originalColor = {0, 0.33, 0.61} -- 原始蓝色
    statusBar.warningColor = {0.8, 0.2, 0.2}  -- 警告红色
    statusBar.warningThreshold = 0.2 -- 20%时显示警告色

    -- 设置初始颜色
    -- statusBar:SetStatusBarColor(statusBar.originalColor[1], statusBar.originalColor[2], statusBar.originalColor[3])

    DebugPrint("|cff00ff00ScenarioBlocks|r: 挑战模式进度条已创建并配置倒计时联动")
end

-- 创建Glow动画效果
-- 对应XML中的Glow动画：Scale + Alpha动画组合
function ScenarioBlocksFrameManager:CreateGlowAnimation(frame)
    if not frame.Glow then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - Glow纹理不存在")
        return
    end

    -- 创建动画组
    local animGroup = frame.Glow:CreateAnimationGroup()
    frame.Glow.Anim = animGroup

    -- Scale动画：startDelay="0.067" scaleX="3" scaleY="1" duration="0.433" order="1"
    local scaleAnim = animGroup:CreateAnimation("Scale")
    scaleAnim:SetStartDelay(0.067)
    scaleAnim:SetScale(3, 1)
    scaleAnim:SetDuration(0.433)
    scaleAnim:SetOrder(1)
    scaleAnim:SetOrigin("LEFT", 0, 0) -- 对应XML中的Origin point="LEFT"
    frame.Glow.ScaleGlow = scaleAnim

    -- Alpha动画1：startDelay="0.067" fromAlpha="0" toAlpha="1" duration="0.1" order="1"
    -- 从0变为1，变化量为+1
    local alphaAnim1 = animGroup:CreateAnimation("Alpha")
    alphaAnim1:SetStartDelay(0.067)
    alphaAnim1:SetChange(1) -- 从当前alpha值(0)增加1，变为1
    alphaAnim1:SetDuration(0.1)
    alphaAnim1:SetOrder(1)

    -- Alpha动画2：startDelay="0.467" fromAlpha="1" toAlpha="0" duration="0.267" order="1"
    -- 从1变为0，变化量为-1
    local alphaAnim2 = animGroup:CreateAnimation("Alpha")
    alphaAnim2:SetStartDelay(0.467)
    alphaAnim2:SetChange(-1) -- 从当前alpha值(1)减少1，变为0
    alphaAnim2:SetDuration(0.267)
    alphaAnim2:SetOrder(1)

    DebugPrint("|cff00ff00ScenarioBlocks|r: Glow动画创建完成")
end

-- 创建CheckFlash动画效果
-- 对应XML中的CheckFlash动画：Alpha + Scale动画组合
function ScenarioBlocksFrameManager:CreateCheckFlashAnimation(frame)
    if not frame.CheckFlash then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - CheckFlash纹理不存在")
        return
    end

    -- 创建动画组
    local animGroup = frame.CheckFlash:CreateAnimationGroup()
    frame.CheckFlash.Anim = animGroup

    -- Alpha动画1：fromAlpha="0" toAlpha="1" duration="0.067" order="1"
    -- 从0变为1，变化量为+1
    local alphaAnim1 = animGroup:CreateAnimation("Alpha")
    alphaAnim1:SetChange(1) -- 从当前alpha值(0)增加1，变为1
    alphaAnim1:SetDuration(0.067)
    alphaAnim1:SetOrder(1)

    -- Scale动画：scaleX="1.25" scaleY="1.25" duration="0.2" order="2"
    local scaleAnim = animGroup:CreateAnimation("Scale")
    scaleAnim:SetScale(1.25, 1.25)
    scaleAnim:SetDuration(0.2)
    scaleAnim:SetOrder(2)

    -- Alpha动画2：fromAlpha="1" toAlpha="0" startDelay="0.167" duration="0.23" order="2"
    -- 从1变为0，变化量为-1
    local alphaAnim2 = animGroup:CreateAnimation("Alpha")
    alphaAnim2:SetChange(-1) -- 从当前alpha值(1)减少1，变为0
    alphaAnim2:SetStartDelay(0.167)
    alphaAnim2:SetDuration(0.23)
    alphaAnim2:SetOrder(2)

    DebugPrint("|cff00ff00ScenarioBlocks|r: CheckFlash动画创建完成")
end

-- 创建Sheen动画效果
-- 对应XML中的Sheen动画：Translation + Alpha动画组合
function ScenarioBlocksFrameManager:CreateSheenAnimation(frame)
    if not frame.Sheen then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - Sheen纹理不存在")
        return
    end

    -- 创建动画组
    local animGroup = frame.Sheen:CreateAnimationGroup()
    frame.Sheen.Anim = animGroup

    -- Translation动画：startDelay="0.067" offsetX="155" offsetY="0" duration="0.467" order="1"
    local translateAnim = animGroup:CreateAnimation("Translation")
    translateAnim:SetStartDelay(0.067)
    translateAnim:SetOffset(155, 0)
    translateAnim:SetDuration(0.467)
    translateAnim:SetOrder(1)

    -- Alpha动画1：startDelay="0.067" fromAlpha="0" toAlpha="1" duration="0.133" order="1"
    -- 从0变为1，变化量为+1
    local alphaAnim1 = animGroup:CreateAnimation("Alpha")
    alphaAnim1:SetStartDelay(0.067)
    alphaAnim1:SetChange(1) -- 从当前alpha值(0)增加1，变为1
    alphaAnim1:SetDuration(0.133)
    alphaAnim1:SetOrder(1)

    -- Alpha动画2：startDelay="0.2" fromAlpha="1" toAlpha="0" duration="0.133" order="1" smoothing="IN"
    -- 从1变为0，变化量为-1
    local alphaAnim2 = animGroup:CreateAnimation("Alpha")
    alphaAnim2:SetStartDelay(0.2)
    alphaAnim2:SetChange(-1) -- 从当前alpha值(1)减少1，变为0
    alphaAnim2:SetDuration(0.133)
    alphaAnim2:SetOrder(1)
    -- 注意：WoW 3.3.5可能不支持smoothing属性，这里省略

    DebugPrint("|cff00ff00ScenarioBlocks|r: Sheen动画创建完成")
end

-- 创建 ObjectiveTrackerCheckLine（从 ObjectiveTrackerCheckLineTemplate XML 转换）
function ScenarioBlocksFrameManager:CreateObjectiveTrackerCheckLine(parent, count)
    -- 参数验证：如果count为nil或小于1，则默认创建1个框架
    local frameCount = count and count > 0 and count or 1
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建 " .. frameCount .. " 个ObjectiveTrackerCheckLine框架")

    local frames = {} -- 存储所有创建的框架
    local previousFrame = nil -- 用于垂直布局的前一个框架引用

    for i = 1, frameCount do
        local frameName = "ObjectiveTrackerCheckLine" .. i
        DebugPrint("|cff00ff00ScenarioBlocks|r: 创建框架 " .. frameName)

        local frame = CreateFrame("Frame", frameName, parent)
        frame:SetSize(232, 16)

        -- 垂直布局：第一个框架锚定到父框架顶部，后续框架锚定到前一个框架底部
        if i == 1 then
            frame:SetPoint("BOTTOMLEFT", parent, "BOTTOMLEFT", 0, -20)
        else
            frame:SetPoint("TOPLEFT", previousFrame, "BOTTOMLEFT", 0, -3)
        end
        frame:Show()

        -- ARTWORK: Text (inherits ObjectiveFont)
        local text = frame:CreateFontString(frameName .. "Text", "ARTWORK", "GameFontHighlight")
        text:SetPoint("TOPLEFT", frame, "TOPLEFT", 20, 0)
        text:SetText("目标 " .. i)
        frame.Text = text

        -- ARTWORK: IconAnchor + Icon
        local iconAnchor = frame:CreateTexture(nil, "ARTWORK")
        iconAnchor:SetSize(16, 16)
        iconAnchor:SetPoint("TOPLEFT", frame, "TOPLEFT", 1, 2)
        frame.IconAnchor = iconAnchor

        -- 使用Atlas纹理创建图标
        -- local icon = self:CreateAtlasTexture(frame, "Objective-Nub", "ARTWORK", 0, true)
        local icon = frame:CreateTexture(nil, "ARTWORK")
        icon:SetSize(16, 16)
        icon:SetPoint("CENTER", iconAnchor, "CENTER")
        frame.Icon = icon

        -- ARTWORK: Glow
        local glow = frame:CreateTexture(nil, "ARTWORK")
        glow:SetTexture("Interface\\Scenarios\\Objective-Lineglow")
        glow:SetAlpha(0)
        glow:SetBlendMode("ADD")
        glow:SetSize(80, 0)
        glow:SetPoint("LEFT", text, "LEFT", -2, 0)
        glow:SetPoint("TOP", frame, "TOP", 0, 0)
        glow:SetPoint("BOTTOM", frame, "BOTTOM", 0, -4)
        frame.Glow = glow

        -- OVERLAY: CheckFlash
        local checkFlash = frame:CreateTexture(nil, "OVERLAY")
        checkFlash:SetTexture("Interface\\Scenarios\\ScenarioIcon-Check")
        checkFlash:SetAlpha(0)
        checkFlash:SetBlendMode("ADD")
        checkFlash:SetSize(16, 16)
        checkFlash:SetPoint("CENTER", icon, "CENTER")
        checkFlash:Hide()
        frame.CheckFlash = checkFlash

        -- OVERLAY: Sheen
        local sheen = frame:CreateTexture(nil, "OVERLAY")
        sheen:SetTexture("Interface\\Scenarios\\Objective-Sheen")
        sheen:SetAlpha(0)
        sheen:SetSize(32, 0)
        sheen:SetPoint("LEFT", glow, "LEFT")
        sheen:SetPoint("TOP", frame, "TOP", 0, 2)
        sheen:SetPoint("BOTTOM", frame, "BOTTOM", 0, -14)
        frame.Sheen = sheen

        -- 创建动画效果
        self:CreateGlowAnimation(frame)
        self:CreateCheckFlashAnimation(frame)
        self:CreateSheenAnimation(frame)

        -- Scripts
        frame:SetScript("OnLoad", function(self)
            local width = _G.OBJECTIVE_TRACKER_TEXT_WIDTH or 232
            if self.Text then self.Text:SetWidth(width) end
        end)
        frame:SetScript("OnHide", function(self)
            if ObjectiveTrackerCheckLine_OnHide then
                ObjectiveTrackerCheckLine_OnHide(self)
            else
                DebugPrint("|cffff0000ScenarioBlocks|r: ObjectiveTrackerCheckLine_OnHide 未定义")
            end
        end)

        self:PlayAllCheckLineAnimations(frame)

        -- 将框架添加到返回数组中
        table.insert(frames, frame)
        previousFrame = frame

        DebugPrint("|cff00ff00ScenarioBlocks|r: 框架 " .. frameName .. " 创建完成")
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 所有 " .. frameCount .. " 个ObjectiveTrackerCheckLine框架创建完成")

    -- 创建BonusTrackerProgressBar
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建BonusTrackerProgressBar")
    local bonusProgressBar = self:CreateBonusTrackerProgressBar(parent, "BonusTrackerProgressBar_" .. math.random(1000, 9999))

    if bonusProgressBar and frameCount > 0 then
        -- 将BonusTrackerProgressBar定位在最后一个ObjectiveTrackerCheckLine框架的底部
        local lastFrame = frames[frameCount]
        bonusProgressBar:SetPoint("TOPLEFT", lastFrame, "BOTTOMLEFT", 0, -8)
        -- bonusProgressBar:Hide() -- 默认隐藏

        -- 保存到父框架的属性中
        parent.BonusProgressBar = bonusProgressBar

        DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar已创建并定位在最后一个CheckLine下方")
    elseif bonusProgressBar then
        -- 如果没有CheckLine框架，则定位在父框架底部
        bonusProgressBar:SetPoint("TOPLEFT", parent, "TOPLEFT", 0, -20)
        bonusProgressBar:Hide() -- 默认隐藏

        -- 保存到父框架的属性中
        parent.BonusProgressBar = bonusProgressBar

        DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar已创建并定位在父框架顶部")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: BonusTrackerProgressBar创建失败")
    end

    return frames
end
-- 便捷函数：批量设置ObjectiveTrackerCheckLine的文本和图标
-- @param frames table 由CreateObjectiveTrackerCheckLine返回的框架数组
-- @param objectives table 目标数据数组，格式：{{text="目标1", icon="Atlas名称"}, ...}
function ScenarioBlocksFrameManager:SetObjectiveTrackerCheckLineData(frames, objectives)
    if not frames or not objectives then
        DebugPrint("|cffff0000ScenarioBlocks|r: SetObjectiveTrackerCheckLineData 参数无效")
        return
    end

    for i, frame in ipairs(frames) do
        if objectives[i] then
            local objective = objectives[i]

            -- 设置文本
            if frame.Text and objective.text then
                frame.Text:SetText(objective.text)
            end

            -- 设置图标（如果提供了Atlas名称）
            if frame.Icon and objective.icon then
                -- 使用CreateAtlasTexture重新设置图标
                local newIcon = self:CreateAtlasTexture(frame, objective.icon, "ARTWORK", 0, true)
                newIcon:SetPoint("CENTER", frame.IconAnchor, "CENTER")
                frame.Icon = newIcon
            end

            DebugPrint("|cff00ff00ScenarioBlocks|r: 设置框架 " .. i .. " 数据: " .. (objective.text or "无文本"))
        end
    end
end

-- 创建演示用的目标追踪检查线框架
-- @param parentFrame Frame 父框架，用于承载ObjectiveTrackerCheckLine框架
-- @param count number 要创建的框架数量（可选，默认为10）
-- @param customObjectives table 自定义目标数据数组（可选），格式：{{text="文本", icon="图标名称"}, ...}
-- @return table 返回创建的checkLines框架数组
--
-- 使用示例：
-- 1. 使用默认数据：self:CreateDemoObjectiveLines(frame, 5)
-- 2. 使用自定义数据：self:CreateDemoObjectiveLines(frame, 3, {{text="自定义目标", icon="Objective-Nub"}})
function ScenarioBlocksFrameManager:CreateDemoObjectiveLines(parentFrame, count, customObjectives)
    local frameCount = count or 10
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建演示用目标追踪线，数量: " .. frameCount)

    -- 创建ObjectiveTrackerCheckLine框架
    local checkLines = self:CreateObjectiveTrackerCheckLine(parentFrame, frameCount)
    parentFrame.CheckLines = checkLines -- 保存框架数组引用

    -- 根据参数选择使用自定义数据还是默认演示数据
    local objectives
    if customObjectives and type(customObjectives) == "table" and #customObjectives > 0 then
        -- 使用传入的自定义目标数据
        objectives = customObjectives
        DebugPrint("|cff00ff00ScenarioBlocks|r: 使用自定义目标数据，数量: " .. #objectives)
    else
        -- 使用默认演示数据
        objectives = {
            {text = "击败10个敌人", icon = "Objective-Nub"},
            {text = "收集5个物品",  icon = "Tracker-Check"},
            {text = "到达指定地点", icon = "Objective-Fail"},
            {text = "击败9个敌人", icon = "Objective-Nub"},
            {text = "收集9个物品", icon = "Tracker-Check"},
            {text = "9到达指定地点", icon = "Objective-Fail"},
            {text = "击败8个敌人", icon = "Objective-Nub"},
            {text = "收集8个物品", icon = "Tracker-Check"},
            {text = "8到达指定地点", icon = "Objective-Fail"},
            {text = "击败10个敌人", icon = "Objective-Nub"},
            {text = "收集10个物品", icon = "Tracker-Check"},
            {text = "10到达指定地点", icon = "Objective-Fail"},
        }
        DebugPrint("|cff00ff00ScenarioBlocks|r: 使用默认演示数据")
    end

    -- 批量设置目标数据
    self:SetObjectiveTrackerCheckLineData(checkLines, objectives)

    DebugPrint("|cff00ff00ScenarioBlocks|r: 演示用目标追踪线创建完成")
    return checkLines
end

-- 创建ScenarioProvingGroundsBlock框架
function ScenarioBlocksFrameManager:CreateScenarioProvingGroundsBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock")

    local frame = CreateFrame("Frame", "ScenarioProvingGroundsBlock", parent)
    frame:SetSize(201, 77) -- 对应XML中的Size x="201" y="77"
    frame:SetPoint("LEFT", parent, "LEFT", 0, 0)
    frame:Show() -- 对应XML中的hidden="true"

    -- 创建层级结构
    self:CreateProvingGroundsBlockLayers(frame)
    self:CreateProvingGroundsBlockFrames(frame)

    -- 创建演示用的目标追踪检查线框架
    self:CreateDemoObjectiveLines(frame, OBJECTIVES_COUNT, OBJECTIVES_TABLE)

    parent.ScenarioProvingGroundsBlock = frame
    return frame
end

-- 创建ScenarioProvingGroundsBlock的层级结构
function ScenarioBlocksFrameManager:CreateProvingGroundsBlockLayers(frame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock的层级结构")
    -- BACKGROUND层
    local bg = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BACKGROUND", 0, true)
    bg:SetPoint("TOPLEFT", frame, 0, 0)
    frame.BG = bg -- 对应XML中的parentKey="BG"

    -- BORDER层
    local goldCurlies = self:CreateAtlasTexture(frame, "ScenarioTrackerToast-FinalFiligree", "BORDER", 0, true)
    goldCurlies:SetPoint("TOPLEFT", frame, 4, -4)
    frame.GoldCurlies = goldCurlies -- 对应XML中的parentKey="GoldCurlies"

    -- ARTWORK层
    self:CreateProvingGroundsArtworkLayer(frame)
end

-- 创建ScenarioProvingGroundsBlock的ARTWORK层
function ScenarioBlocksFrameManager:CreateProvingGroundsArtworkLayer(frame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock的ARTWORK层")
    -- MedalIcon纹理
    local medalIcon = frame:CreateTexture(nil, "ARTWORK")
    medalIcon:SetSize(52, 52) -- 对应XML中的Size x="52" y="52"
    medalIcon:SetPoint("LEFT", frame, "LEFT", 5, -1)
    medalIcon:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    medalIcon:SetTexture("Interface\\Challenges\\challenges-plat") -- 对应XML中的file属性
    frame.MedalIcon = medalIcon -- 对应XML中的parentKey="MedalIcon"

    -- WaveLabel字体字符串
    local waveLabel = frame:CreateFontString(nil, "ARTWORK", "QuestFont_Large")
    waveLabel:SetPoint("TOPLEFT", medalIcon, "TOPRIGHT", 1, -4)
    waveLabel:SetText("波次：") -- 对应XML中的text属性
    waveLabel:SetTextColor(1.0, 0.82, 0) -- 对应XML中的Color r="1.0" g="0.82" b="0"
    waveLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    waveLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.WaveLabel = waveLabel -- 对应XML中的parentKey="WaveLabel"

    -- Wave字体字符串
    local wave = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightLarge")
    wave:SetPoint("BOTTOMLEFT", waveLabel, "BOTTOMRIGHT", 4, -1)
    wave:SetText("90") -- 对应XML中的text="0"
    frame.Wave = wave -- 对应XML中的parentKey="Wave"

    -- ScoreLabel字体字符串
    local scoreLabel = frame:CreateFontString(nil, "ARTWORK", "QuestFont_Large")
    scoreLabel:SetPoint("TOPLEFT", waveLabel, "BOTTOMLEFT", 0, -3)
    scoreLabel:SetText("分数：") -- 对应XML中的text属性
    scoreLabel:Show() -- 对应XML中的hidden="true"
    scoreLabel:SetTextColor(1.0, 0.82, 0) -- 对应XML中的Color
    scoreLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    scoreLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.ScoreLabel = scoreLabel -- 对应XML中的parentKey="ScoreLabel"

    -- Score字体字符串
    local score = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightLarge")
    score:SetPoint("BOTTOMLEFT", scoreLabel, "BOTTOMRIGHT", 4, -1)
    score:SetText("999") -- 对应XML中的text="0"
    score:Show() -- 对应XML中的hidden="true"
    frame.Score = score -- 对应XML中的parentKey="Score"
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock的ARTWORK层完成")
end

-- 创建ScenarioProvingGroundsBlock的子框架
function ScenarioBlocksFrameManager:CreateProvingGroundsBlockFrames(frame)
    -- 创建StatusBar
    local statusBar = CreateFrame("StatusBar", nil, frame)
    statusBar:SetPoint("BOTTOM", frame, 40, 10)
    statusBar:SetSize(177, 10) -- 对应XML中的Size x="177" y="15"
    -- statusBar:SetParentLevel(true) -- 对应XML中的useParentLevel="true"，在3.3.5中可能不支持
    frame.StatusBar = statusBar -- 对应XML中的parentKey="StatusBar"

    -- 创建StatusBar的层级
    self:CreateProvingGroundsStatusBarLayers(statusBar)

    -- 设置状态栏纹理和颜色
    statusBar:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar") -- 对应XML中的BarTexture file
    statusBar:SetStatusBarColor(0, 0.33, 0.61) -- 对应XML中的BarColor r="0" g="0.33" b="0.61"
    statusBar:SetMinMaxValues(0, 1000)
    statusBar:SetValue(300)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock的子框架完成！！！")
end

-- 创建试炼场状态栏的层级
function ScenarioBlocksFrameManager:CreateProvingGroundsStatusBarLayers(statusBar)
    -- OVERLAY层
    local borderTexture = self:CreateAtlasTexture(statusBar, "challenges-timerborder", "OVERLAY", 0, false)
    borderTexture:SetSize(184, 20) -- 对应XML中的Size x="184" y="25"
    borderTexture:SetPoint("CENTER", statusBar, "CENTER", 0, 0)

    local timeLeft = statusBar:CreateFontString(nil, "OVERLAY", "GameFontHighlight")
    timeLeft:SetJustifyH("CENTER") -- 对应XML中的justifyH="CENTER"
    statusBar.TimeLeft = timeLeft -- 对应XML中的parentKey="TimeLeft"
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建试炼场状态栏的层级完成")
end

-- 设置ScenarioBlocksFrame的脚本事件
function ScenarioBlocksFrameManager:SetupScenarioBlocksFrameScripts(frame)
    -- OnLoad事件
    frame:SetScript("OnLoad", function(self)
        if ScenarioBlocksFrame_OnLoad then
            ScenarioBlocksFrame_OnLoad(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioBlocksFrame_OnLoad函数未定义")
        end
    end)

    -- OnEvent事件
    frame:SetScript("OnEvent", function(self, event, ...)
        if ScenarioBlocksFrame_OnEvent then
            ScenarioBlocksFrame_OnEvent(self, event, ...)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioBlocksFrame_OnEvent函数未定义")
        end
    end)
end

function ScenarioBlocksFrameManager:Scenario_ChallengeMode_TimesUpLootStatus_OnEnter(frame)
	local block = frame:GetParent();

	GameTooltip:SetOwner(frame, "ANCHOR_RIGHT");
	GameTooltip:SetText("时间结束", 1, 1, 1);
	local line;
	if (block.wasDepleted) then
		if (UnitIsGroupLeader("player")) then
			line = "你的钥石无法升级，且你在完成此地下城后将无法获得战利品宝箱。你可以右键点击头像并选择“重置史诗地下城”来重新开始挑战。";
		else
			line = "你的钥石无法升级，且你在完成此地下城后将无法获得战利品宝箱。小队队长可以右键点击头像并选择“重置史诗地下城”来重新开始挑战。";
		end
	else
		line = "你的钥石无法升级。但你完成此地下城后仍可获得战利品宝箱。";
	end
	GameTooltip:AddLine(line, nil, nil, nil, true);
	GameTooltip:Show();
end

-- 便捷函数：创建ScenarioBlocksFrame实例
-- @param parentFrame Frame 父框架
-- @param frameName string 框架名称（可选）
-- @param blockTypes table 要创建的子框架类型数组（可选），可选值：{"ObjectiveBlock", "StageBlock", "ChallengeModeBlock", "ProvingGroundsBlock"}
-- @return Frame 创建的ScenarioBlocksFrame
function CreateScenarioBlocksFrame(parentFrame, frameName, blockTypes)
    if not parentFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - 父框架不能为空")
        return nil
    end
    if blockTypes and type(blockTypes) == "string" then
        local typeName = blockTypes
        blockTypes = {}
        table.insert(blockTypes, typeName)
    end
    return ScenarioBlocksFrameManager:CreateScenarioBlocksFrame(parentFrame, frameName, blockTypes)
end

-- 全局API：中文子框架类型名称转换为英文blockType
-- @param chineseName string 中文的子框架类型名称
-- @return string|nil 对应的英文blockType字符串，匹配失败时返回nil
--
-- 支持的中文名称映射：
-- "目标块" 或 "目标框架" → "ObjectiveBlock"
-- "阶段块" 或 "阶段框架" → "StageBlock"
-- "挑战模式块" 或 "挑战模式框架" → "ChallengeModeBlock"
-- "试炼场块" 或 "试炼场框架" → "ProvingGroundsBlock"
--
-- 使用示例：
-- local blockType = GetScenarioBlockTypeFromChineseName("阶段块")
-- if blockType then
--     print("转换结果: " .. blockType) -- 输出: "StageBlock"
-- end
function GetScenarioBlockTypeFromChineseName(chineseName)
    -- 参数验证
    if not chineseName then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetScenarioBlockTypeFromChineseName - 参数不能为空")
        return nil
    end

    if type(chineseName) ~= "string" then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetScenarioBlockTypeFromChineseName - 参数必须是字符串，当前类型: " .. type(chineseName))
        return nil
    end

    if string.len(chineseName) == 0 then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetScenarioBlockTypeFromChineseName - 参数不能为空字符串")
        return nil
    end

    -- 调用内部实现
    return ScenarioBlocksFrameManager:GetBlockTypeFromChineseName(chineseName)
end

-- 全局API：批量转换中文子框架类型名称数组为英文blockTypes数组
-- @param chineseNames table 中文子框架类型名称数组
-- @return table|nil 转换后的英文blockTypes数组，如果输入无效或转换失败则返回nil
--
-- 使用示例：
-- local chineseNames = {"阶段块", "试炼场框架", "挑战模式块"}
-- local blockTypes = ConvertChineseNamesToBlockTypes(chineseNames)
-- if blockTypes then
--     -- blockTypes = {"StageBlock", "ProvingGroundsBlock", "ChallengeModeBlock"}
--     CreateScenarioBlocksFrame(parent, "MyFrame", blockTypes)
-- end
function ConvertChineseNamesToBlockTypes(chineseNames)
    -- 参数验证
    if not chineseNames then
        DebugPrint("|cffff0000ScenarioBlocks|r: ConvertChineseNamesToBlockTypes - 参数不能为空")
        return nil
    end

    if type(chineseNames) ~= "table" then
        DebugPrint("|cffff0000ScenarioBlocks|r: ConvertChineseNamesToBlockTypes - 参数必须是数组，当前类型: " .. type(chineseNames))
        return nil
    end

    if #chineseNames == 0 then
        DebugPrint("|cffff0000ScenarioBlocks|r: ConvertChineseNamesToBlockTypes - 参数数组不能为空")
        return nil
    end

    -- 调用内部实现
    return ScenarioBlocksFrameManager:ConvertChineseNamesToBlockTypes(chineseNames)
end

-- 全局API：启动挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @param minutes number 倒计时分钟数（可选，默认40分钟）
-- @return boolean 是否成功启动
--
-- 使用示例：
-- local success = StartChallengeModeCountdown(challengeFrame, 30) -- 启动30分钟倒计时
-- if success then
--     print("倒计时已启动")
-- end
function StartChallengeModeCountdown(challengeModeFrame, minutes)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: StartChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    local initialSeconds = nil
    if minutes and type(minutes) == "number" and minutes > 0 then
        initialSeconds = minutes * 60
    end

    ScenarioBlocksFrameManager:StartCountdown(challengeModeFrame, initialSeconds)
    return true
end

-- 全局API：暂停挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @return boolean 是否成功暂停
function PauseChallengeModeCountdown(challengeModeFrame)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: PauseChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    ScenarioBlocksFrameManager:PauseCountdown(challengeModeFrame)
    return true
end

-- 全局API：恢复挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @return boolean 是否成功恢复
function ResumeChallengeModeCountdown(challengeModeFrame)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: ResumeChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    ScenarioBlocksFrameManager:ResumeCountdown(challengeModeFrame)
    return true
end

-- 全局API：停止挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @return boolean 是否成功停止
function StopChallengeModeCountdown(challengeModeFrame)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: StopChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    ScenarioBlocksFrameManager:StopCountdown(challengeModeFrame)
    return true
end

-- 全局API：重置挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @return boolean 是否成功重置
function ResetChallengeModeCountdown(challengeModeFrame)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: ResetChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    ScenarioBlocksFrameManager:ResetCountdown(challengeModeFrame)
    return true
end

-- 全局API：获取挑战模式倒计时剩余时间
-- @param challengeModeFrame Frame 挑战模式框架
-- @return number|nil 剩余秒数，失败时返回nil
function GetChallengeModeCountdownTime(challengeModeFrame)
    if not challengeModeFrame or not challengeModeFrame.countdownData then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetChallengeModeCountdownTime - 框架或倒计时数据无效")
        return nil
    end

    return challengeModeFrame.countdownData.remainingSeconds
end

-- 全局API：设置挑战模式进度条联动状态
-- @param challengeModeFrame Frame 挑战模式框架
-- @param enabled boolean 是否启用联动
-- @return boolean 是否成功设置
function SetChallengeModeProgressBarLinked(challengeModeFrame, enabled)
    if not challengeModeFrame or not challengeModeFrame.StatusBar then
        DebugPrint("|cffff0000ScenarioBlocks|r: SetChallengeModeProgressBarLinked - 框架或进度条无效")
        return false
    end

    challengeModeFrame.StatusBar.countdownLinked = enabled
    DebugPrint("|cff00ff00ScenarioBlocks|r: 进度条联动状态已设置为: " .. tostring(enabled))

    -- 如果启用联动，立即更新一次进度条
    if enabled and challengeModeFrame.countdownData then
        ScenarioBlocksFrameManager:UpdateCountdownProgressBar(challengeModeFrame)
    end

    return true
end

-- 全局API：获取挑战模式进度条当前值
-- @param challengeModeFrame Frame 挑战模式框架
-- @return number|nil 进度条当前值（0-1000），失败时返回nil
function GetChallengeModeProgressBarValue(challengeModeFrame)
    if not challengeModeFrame or not challengeModeFrame.StatusBar then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetChallengeModeProgressBarValue - 框架或进度条无效")
        return nil
    end

    return challengeModeFrame.StatusBar:GetValue()
end

-- 注册斜杠命令的函数
function ScenarioBlocksFrameManager:RegisterSlashCommands()
    -- 注册 /scenario 命令
    SLASH_SCENARIOTEST1 = "/scenario"
    SLASH_SCENARIOTEST2 = "/testscenario"

    SlashCmdList["SCENARIOTEST"] = function(msg)
        DebugPrint("ScenarioBlocks: 收到命令参数: '" .. (msg or "nil") .. "'")
        local cmd = string.lower(msg or "")

        if cmd == "show" or cmd == "" then
            -- 创建或显示测试框架
            if not TheScenarioFrame then
                DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建测试框架...")
                -- 创建包含挑战模式块的测试框架，以便测试倒计时功能
                local blockTypes = {"ChallengeModeBlock"}
                TheScenarioFrame = CreateScenarioBlocksFrame(UIParent, "TestScenarioBlocksFrame", blockTypes)
                if TheScenarioFrame then
                    TheScenarioFrame:SetPoint("CENTER", UIParent, "CENTER", 0, 0)
                    DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已创建（包含挑战模式块）")
                else
                    DebugPrint("|cffff0000ScenarioBlocks|r: 测试框架创建失败")
                    return
                end
            end
            TheScenarioFrame:Show()
            DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已显示")

        elseif cmd == "hide" then
            -- 隐藏测试框架
            if TheScenarioFrame then
                TheScenarioFrame:Hide()
                DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已隐藏")
            else
                DebugPrint("|cffff0000ScenarioBlocks|r: 测试框架不存在")
            end

        elseif cmd == "debug on" then
            -- 开启调试模式
            DEBUG_ENABLED = true
            print("|cff00ff00ScenarioBlocks|r: 调试模式已开启")

        elseif cmd == "debug off" then
            -- 关闭调试模式
            DEBUG_ENABLED = false
            print("|cff00ff00ScenarioBlocks|r: 调试模式已关闭")

        elseif cmd == "timer start" then
            -- 启动倒计时测试
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                local challengeFrame = TheScenarioFrame.ScenarioChallengeModeBlock
                StartChallengeModeCountdown(challengeFrame, 5) -- 测试用5分钟
                print("|cff00ff00ScenarioBlocks|r: 倒计时已启动（5分钟测试）")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer pause" then
            -- 暂停倒计时
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                PauseChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock)
                print("|cff00ff00ScenarioBlocks|r: 倒计时已暂停")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer resume" then
            -- 恢复倒计时
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                ResumeChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock)
                print("|cff00ff00ScenarioBlocks|r: 倒计时已恢复")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer stop" then
            -- 停止倒计时
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                StopChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock)
                print("|cff00ff00ScenarioBlocks|r: 倒计时已停止")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer reset" then
            -- 重置倒计时
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                ResetChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock)
                print("|cff00ff00ScenarioBlocks|r: 倒计时已重置")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer status" then
            -- 查看倒计时状态
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                local remainingTime = GetChallengeModeCountdownTime(TheScenarioFrame.ScenarioChallengeModeBlock)
                local progressValue = GetChallengeModeProgressBarValue(TheScenarioFrame.ScenarioChallengeModeBlock)
                if remainingTime and progressValue then
                    local timeText = ScenarioBlocksFrameManager:FormatTime(remainingTime)
                    local progressPercent = (progressValue / 1000) * 100
                    print("|cff00ff00ScenarioBlocks|r: 倒计时剩余时间: " .. timeText)
                    print("|cff00ff00ScenarioBlocks|r: 进度条进度: " .. string.format("%.1f%%", progressPercent))
                else
                    print("|cffff0000ScenarioBlocks|r: 无法获取倒计时状态")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "progress toggle" then
            -- 切换进度条联动状态
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                local statusBar = TheScenarioFrame.ScenarioChallengeModeBlock.StatusBar
                if statusBar then
                    local newState = not statusBar.countdownLinked
                    SetChallengeModeProgressBarLinked(TheScenarioFrame.ScenarioChallengeModeBlock, newState)
                    print("|cff00ff00ScenarioBlocks|r: 进度条联动已" .. (newState and "启用" or "禁用"))
                else
                    print("|cffff0000ScenarioBlocks|r: 进度条不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "anim glow" then
            -- 测试Glow动画
            if TheScenarioFrame and TheScenarioFrame.CheckLines then
                local checkLine = TheScenarioFrame.CheckLines[1] -- 使用第一个CheckLine
                if checkLine then
                    ScenarioBlocksFrameManager:PlayGlowAnimation(checkLine)
                    print("|cff00ff00ScenarioBlocks|r: 播放Glow动画")
                else
                    print("|cffff0000ScenarioBlocks|r: CheckLine不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "anim check" then
            -- 测试CheckFlash动画
            if TheScenarioFrame and TheScenarioFrame.CheckLines then
                local checkLine = TheScenarioFrame.CheckLines[1] -- 使用第一个CheckLine
                if checkLine then
                    ScenarioBlocksFrameManager:PlayCheckFlashAnimation(checkLine)
                    print("|cff00ff00ScenarioBlocks|r: 播放CheckFlash动画")
                else
                    print("|cffff0000ScenarioBlocks|r: CheckLine不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "anim sheen" then
            -- 测试Sheen动画
            if TheScenarioFrame and TheScenarioFrame.CheckLines then
                local checkLine = TheScenarioFrame.CheckLines[1] -- 使用第一个CheckLine
                if checkLine then
                    ScenarioBlocksFrameManager:PlaySheenAnimation(checkLine)
                    print("|cff00ff00ScenarioBlocks|r: 播放Sheen动画")
                else
                    print("|cffff0000ScenarioBlocks|r: CheckLine不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "anim all" then
            -- 测试所有动画
            if TheScenarioFrame and TheScenarioFrame.CheckLines then
                local checkLine = TheScenarioFrame.CheckLines[1] -- 使用第一个CheckLine
                if checkLine then
                    ScenarioBlocksFrameManager:PlayAllCheckLineAnimations(checkLine)
                    print("|cff00ff00ScenarioBlocks|r: 播放所有CheckLine动画")
                else
                    print("|cffff0000ScenarioBlocks|r: CheckLine不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        else
            -- 显示帮助信息
            print("|cff00ff00ScenarioBlocks 测试命令:|r")
            print("  /scenario show - 显示测试框架")
            print("  /scenario hide - 隐藏测试框架")
            print("  /scenario debug on - 开启调试模式")
            print("  /scenario debug off - 关闭调试模式")
            print("  |cffFFD700倒计时测试命令:|r")
            print("  /scenario timer start - 启动倒计时（5分钟测试）")
            print("  /scenario timer pause - 暂停倒计时")
            print("  /scenario timer resume - 恢复倒计时")
            print("  /scenario timer stop - 停止倒计时")
            print("  /scenario timer reset - 重置倒计时")
            print("  /scenario timer status - 查看倒计时和进度条状态")
            print("  |cff00FFFF进度条测试命令:|r")
            print("  /scenario progress toggle - 切换进度条联动状态")
        end
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 斜杠命令已注册 - /scenario, /testscenario")
end

-- 事件处理函数
function ScenarioBlocksFrameManager:OnEvent(event, ...)
    if event == "ADDON_LOADED" then
        local addonName = ...
        DebugPrint("ScenarioBlocks addonName: " .. addonName)
        if addonName == "ExtractAtlasInfos" then
            -- 插件加载完成后的初始化
            DebugPrint("|cff00ff00ScenarioBlocks|r: 插件初始化完成")
            -- 注册斜杠命令
            self:RegisterSlashCommands()
        end
    end
end

-- ========================================
-- BonusTrackerProgressBar模板实现
-- ========================================

-- 创建BonusTrackerProgressBar（从BonusTrackerProgressBarTemplate XML转换）
-- @param parent Frame 父框架
-- @param name string 框架名称（可选）
-- @return Frame 创建的进度条框架
function ScenarioBlocksFrameManager:CreateBonusTrackerProgressBar(parent, name)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建BonusTrackerProgressBar")

    -- 创建主框架 - 对应XML中的Frame
    local frameName = name or ("BonusTrackerProgressBar" .. math.random(1000, 9999))
    local frame = CreateFrame("Frame", frameName, parent)
    frame:SetSize(192, 38) -- 对应XML中的Size x="192" y="38"
    frame:SetPoint("CENTER") -- 对应XML中的Anchor point="CENTER"
    frame:Show() -- 对应XML中的hidden="true"

    -- 创建StatusBar子框架 - 对应XML中的StatusBar parentKey="Bar"
    local statusBar = CreateFrame("StatusBar", nil, frame)
    statusBar:SetSize(191, 17) -- 对应XML中的Size x="191" y="17"
    statusBar:SetPoint("LEFT", frame, "LEFT", 10, 0) -- 对应XML中的Anchor point="LEFT" x="10" y="0"
    statusBar:SetMinMaxValues(0, 100) -- 对应XML中的minValue="0" maxValue="100"
    statusBar:SetValue(76) -- 对应XML中的defaultValue="0"
    frame.Bar = statusBar -- 对应XML中的parentKey="Bar"

    -- 设置StatusBar的纹理和颜色
    statusBar:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar") -- 对应XML中的BarTexture
    statusBar:SetStatusBarColor(0.26, 0.42, 1) -- 对应XML中的BarColor r="0.26" g="0.42" b="1"

    -- 创建StatusBar的层级结构
    self:CreateBonusTrackerProgressBarLayers(statusBar)

    -- 创建StatusBar的动画
    self:CreateBonusTrackerProgressBarAnimations(statusBar)

    self:PlayBonusTrackerProgressBarAnimation(frame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar创建完成")
    return frame
end

-- 创建BonusTrackerProgressBar的层级结构
-- @param statusBar StatusBar StatusBar框架
function ScenarioBlocksFrameManager:CreateBonusTrackerProgressBarLayers(statusBar)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建BonusTrackerProgressBar层级结构")

    -- BACKGROUND层 textureSubLevel="-1"
    -- BarBG纹理
    local barBG = statusBar:CreateTexture(nil, "BACKGROUND", nil, -1)
    -- barBG:SetColorTexture(0.04, 0.07, 0.18) -- 对应XML中的Color r="0.04" g="0.07" b="0.18"
    barBG:SetAllPoints(statusBar)
    statusBar.BarBG = barBG -- 对应XML中的parentKey="BarBG"

    -- Icon纹理
    local icon = statusBar:CreateTexture(nil, "BACKGROUND", nil, -1)
    icon:SetSize(32, 32) -- 对应XML中的Size x="32" y="32"
    icon:SetPoint("RIGHT", statusBar, "RIGHT", 33, 2) -- 对应XML中的Anchor point="RIGHT" x="33" y="2"
    statusBar.Icon = icon -- 对应XML中的parentKey="Icon"

    -- ARTWORK层 level="ARTWORK"
    -- BarFrame纹理
    local barFrame = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-frame", "ARTWORK", 0, true)
    barFrame:SetPoint("LEFT", statusBar, "LEFT", -8, -1) -- 对应XML中的Anchor point="LEFT" x="-8" y="-1"
    statusBar.BarFrame = barFrame -- 对应XML中的parentKey="BarFrame"

    -- IconBG纹理
    local iconBG = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-ring", "ARTWORK", 0, true)
    iconBG:SetPoint("RIGHT", barFrame, "RIGHT", 0, 0) -- 对应XML中的Anchor point="RIGHT" relativeKey="$parent.BarFrame"
    statusBar.IconBG = iconBG -- 对应XML中的parentKey="IconBG"

    -- Label字体字符串
    local label = statusBar:CreateFontString(nil, "ARTWORK", "GameFontHighlightMedium")
    label:SetJustifyH("CENTER") -- 对应XML中的justifyH="CENTER"
    label:SetPoint("CENTER", statusBar, "CENTER", -1, -1) -- 对应XML中的Anchor point="CENTER" x="-1" y="-1"
    label:SetText("0%") -- 对应XML中的text="Label"
    statusBar.Label = label -- 对应XML中的parentKey="Label"

    -- ARTWORK层 textureSubLevel="1"
    -- BarFrame2纹理
    local barFrame2 = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-frame", "ARTWORK", 1, true)
    barFrame2:SetPoint("CENTER", barFrame, "CENTER") -- 对应XML中的Anchor point="CENTER" relativeKey="$parent.BarFrame"
    barFrame2:SetAlpha(0) -- 对应XML中的alpha="0"
    barFrame2:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.BarFrame2 = barFrame2 -- 对应XML中的parentKey="BarFrame2"

    -- ARTWORK层 textureSubLevel="2"
    -- BarFrame3纹理
    local barFrame3 = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-frame", "ARTWORK", 2, true)
    barFrame3:SetPoint("CENTER", barFrame, "CENTER") -- 对应XML中的Anchor point="CENTER" relativeKey="$parent.BarFrame"
    barFrame3:SetAlpha(0) -- 对应XML中的alpha="0"
    barFrame3:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.BarFrame3 = barFrame3 -- 对应XML中的parentKey="BarFrame3"

    -- OVERLAY层
    -- BarGlow纹理
    local barGlow = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-glow", "OVERLAY", 0, true)
    barGlow:SetPoint("LEFT", statusBar, "LEFT", -8, -1) -- 对应XML中的Anchor point="LEFT" x="-8" y="-1"
    barGlow:SetAlpha(0) -- 对应XML中的alpha="0"
    barGlow:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.BarGlow = barGlow -- 对应XML中的parentKey="BarGlow"

    -- Sheen纹理
    local sheen = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-sheen", "OVERLAY", 0, false)
    sheen:SetSize(97, 22) -- 对应XML中的Size x="97" y="22"
    sheen:SetPoint("LEFT", barFrame, "LEFT", -60, 0) -- 对应XML中的Anchor point="LEFT" relativeKey="$parent.BarFrame" x="-60" y="0"
    sheen:SetAlpha(0) -- 对应XML中的alpha="0"
    sheen:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.Sheen = sheen -- 对应XML中的parentKey="Sheen"

    -- OVERLAY层 textureSubLevel="1"
    -- Starburst纹理
    local starburst = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-starburst", "OVERLAY", 1, true)
    starburst:SetPoint("TOPRIGHT", barFrame, "TOPRIGHT", 1, 6) -- 对应XML中的Anchor point="TOPRIGHT" relativeKey="$parent.BarFrame" x="1" y="6"
    starburst:SetAlpha(0) -- 对应XML中的alpha="0"
    starburst:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.Starburst = starburst -- 对应XML中的parentKey="Starburst"

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar层级结构创建完成")
end

-- 创建BonusTrackerProgressBar的动画组
-- @param statusBar StatusBar StatusBar框架
function ScenarioBlocksFrameManager:CreateBonusTrackerProgressBarAnimations(statusBar)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建BonusTrackerProgressBar动画")

    -- 创建主StatusBar的AnimIn动画组 - 对应XML中的AnimationGroup parentKey="AnimIn"
    local animIn = statusBar:CreateAnimationGroup()
    statusBar.AnimIn = animIn -- 对应XML中的parentKey="AnimIn"

    -- 主框架Alpha动画：duration="0.1" order="1" fromAlpha="0" toAlpha="1"
    local mainAlpha = animIn:CreateAnimation("Alpha")
    mainAlpha:SetDuration(0.1)
    mainAlpha:SetOrder(1)
    mainAlpha:SetChange(1) -- 从0变为1，变化量为+1

    statusBar.AnimIn:SetScript("OnFinished", function(self)
        statusBar:SetAlpha(1)
    end)

    -- 创建BarGlow的动画组
    local barGlowAnim = statusBar.BarGlow:CreateAnimationGroup()
    statusBar.BarGlow.Anim = barGlowAnim

    -- BarGlow Alpha动画1：startDelay="1.34" duration="0.53" order="1" fromAlpha="0" toAlpha="0.5"
    local barGlowAlpha1 = barGlowAnim:CreateAnimation("Alpha")
    barGlowAlpha1:SetStartDelay(1.34)
    barGlowAlpha1:SetDuration(0.53)
    barGlowAlpha1:SetOrder(1)
    barGlowAlpha1:SetChange(0.5) -- 从0变为0.5，变化量为+0.5

    -- BarGlow Alpha动画2：startDelay="1.87" duration="0.53" order="1" fromAlpha="0.5" toAlpha="0"
    local barGlowAlpha2 = barGlowAnim:CreateAnimation("Alpha")
    barGlowAlpha2:SetStartDelay(1.87)
    barGlowAlpha2:SetDuration(0.53)
    barGlowAlpha2:SetOrder(1)
    barGlowAlpha2:SetChange(-0.5) -- 从0.5变为0，变化量为-0.5

    -- 创建Starburst的动画组
    local starburstAnim = statusBar.Starburst:CreateAnimationGroup()
    statusBar.Starburst.Anim = starburstAnim

    -- Starburst Scale动画1：startDelay="1" duration="0.1" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="0.5"
    local starburstScale1 = starburstAnim:CreateAnimation("Scale")
    starburstScale1:SetStartDelay(1)
    starburstScale1:SetDuration(0.1)
    starburstScale1:SetOrder(1)
    starburstScale1:SetScale(0.5, 0.5) -- 缩放到0.5倍

    -- Starburst Scale动画2：startDelay="1.34" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="2" toScaleY="2"
    local starburstScale2 = starburstAnim:CreateAnimation("Scale")
    starburstScale2:SetStartDelay(1.34)
    starburstScale2:SetDuration(0.5)
    starburstScale2:SetOrder(1)
    starburstScale2:SetScale(2, 2) -- 缩放到2倍

    -- Starburst Scale动画3：startDelay="1.84" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="0.5"
    local starburstScale3 = starburstAnim:CreateAnimation("Scale")
    starburstScale3:SetStartDelay(1.84)
    starburstScale3:SetDuration(0.5)
    starburstScale3:SetOrder(1)
    starburstScale3:SetScale(0.5, 0.5) -- 缩放到0.5倍

    -- Starburst Alpha动画1：startDelay="1.34" duration="0.1" order="1" fromAlpha="0" toAlpha="1"
    local starburstAlpha1 = starburstAnim:CreateAnimation("Alpha")
    starburstAlpha1:SetStartDelay(1.34)
    starburstAlpha1:SetDuration(0.1)
    starburstAlpha1:SetOrder(1)
    starburstAlpha1:SetChange(1) -- 从0变为1，变化量为+1

    -- Starburst Alpha动画2：startDelay="1.44" duration="0.9" order="1" fromAlpha="1" toAlpha="0"
    local starburstAlpha2 = starburstAnim:CreateAnimation("Alpha")
    starburstAlpha2:SetStartDelay(1.44)
    starburstAlpha2:SetDuration(0.9)
    starburstAlpha2:SetOrder(1)
    starburstAlpha2:SetChange(-1) -- 从1变为0，变化量为-1

    -- Starburst Rotation动画1：startDelay="1" duration="0.1" order="1" degrees="-41"
    local starburstRotation1 = starburstAnim:CreateAnimation("Rotation")
    starburstRotation1:SetStartDelay(1)
    starburstRotation1:SetDuration(0.1)
    starburstRotation1:SetOrder(1)
    starburstRotation1:SetDegrees(-41)

    -- Starburst Rotation动画2：startDelay="1.2" duration="1.41" order="1" degrees="-35"
    local starburstRotation2 = starburstAnim:CreateAnimation("Rotation")
    starburstRotation2:SetStartDelay(1.2)
    starburstRotation2:SetDuration(1.41)
    starburstRotation2:SetOrder(1)
    starburstRotation2:SetDegrees(-35)

    -- 创建BarFrame2的动画组
    local barFrame2Anim = statusBar.BarFrame2:CreateAnimationGroup()
    statusBar.BarFrame2.Anim = barFrame2Anim

    -- BarFrame2 Alpha动画1：startDelay="1.34" duration="0.53" order="1" fromAlpha="0" toAlpha="1"
    local barFrame2Alpha1 = barFrame2Anim:CreateAnimation("Alpha")
    barFrame2Alpha1:SetStartDelay(1.34)
    barFrame2Alpha1:SetDuration(0.53)
    barFrame2Alpha1:SetOrder(1)
    barFrame2Alpha1:SetChange(1) -- 从0变为1，变化量为+1

    -- BarFrame2 Alpha动画2：startDelay="1.87" duration="0.53" order="1" fromAlpha="1" toAlpha="0"
    local barFrame2Alpha2 = barFrame2Anim:CreateAnimation("Alpha")
    barFrame2Alpha2:SetStartDelay(1.87)
    barFrame2Alpha2:SetDuration(0.53)
    barFrame2Alpha2:SetOrder(1)
    barFrame2Alpha2:SetChange(-1) -- 从1变为0，变化量为-1

    -- 创建BarFrame3的动画组
    local barFrame3Anim = statusBar.BarFrame3:CreateAnimationGroup()
    statusBar.BarFrame3.Anim = barFrame3Anim

    -- BarFrame3 Alpha动画1：startDelay="1.34" duration="0.53" order="1" fromAlpha="0" toAlpha="1"
    local barFrame3Alpha1 = barFrame3Anim:CreateAnimation("Alpha")
    barFrame3Alpha1:SetStartDelay(1.34)
    barFrame3Alpha1:SetDuration(0.53)
    barFrame3Alpha1:SetOrder(1)
    barFrame3Alpha1:SetChange(1) -- 从0变为1，变化量为+1

    -- BarFrame3 Alpha动画2：startDelay="1.87" duration="0.53" order="1" fromAlpha="1" toAlpha="0"
    local barFrame3Alpha2 = barFrame3Anim:CreateAnimation("Alpha")
    barFrame3Alpha2:SetStartDelay(1.87)
    barFrame3Alpha2:SetDuration(0.53)
    barFrame3Alpha2:SetOrder(1)
    barFrame3Alpha2:SetChange(-1) -- 从1变为0，变化量为-1

    -- 创建Sheen的动画组
    local sheenAnim = statusBar.Sheen:CreateAnimationGroup()
    statusBar.Sheen.Anim = sheenAnim

    -- Sheen Translation动画：startDelay="1.06" duration="0.48" order="1" offsetX="68" offsetY="0"
    local sheenTranslation = sheenAnim:CreateAnimation("Translation")
    sheenTranslation:SetStartDelay(1.06)
    sheenTranslation:SetDuration(0.48)
    sheenTranslation:SetOrder(1)
    sheenTranslation:SetOffset(68, 0)

    -- Sheen Alpha动画1：startDelay="1.09" duration="0.1" order="1" fromAlpha="0" toAlpha="1"
    local sheenAlpha1 = sheenAnim:CreateAnimation("Alpha")
    sheenAlpha1:SetStartDelay(1.09)
    sheenAlpha1:SetDuration(0.1)
    sheenAlpha1:SetOrder(1)
    sheenAlpha1:SetChange(1) -- 从0变为1，变化量为+1

    -- Sheen Alpha动画2：startDelay="1.34" duration="0.05" order="1" fromAlpha="1" toAlpha="0"
    local sheenAlpha2 = sheenAnim:CreateAnimation("Alpha")
    sheenAlpha2:SetStartDelay(1.34)
    sheenAlpha2:SetDuration(0.05)
    sheenAlpha2:SetOrder(1)
    sheenAlpha2:SetChange(-1) -- 从1变为0，变化量为-1

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar动画创建完成")
end

-- 播放BonusTrackerProgressBar的所有动画
-- @param frame Frame BonusTrackerProgressBar框架
function ScenarioBlocksFrameManager:PlayBonusTrackerProgressBarAnimation(frame)
    if not frame or not frame.Bar then
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放BonusTrackerProgressBar动画 - 框架不存在")
        return
    end

    local statusBar = frame.Bar
    DebugPrint("|cff00ff00ScenarioBlocks|r: 播放BonusTrackerProgressBar所有动画")

    -- 播放主StatusBar的AnimIn动画
    if statusBar.AnimIn then
        statusBar.AnimIn:Play()
    end

    -- 播放BarGlow动画
    if statusBar.BarGlow and statusBar.BarGlow.Anim then
        statusBar.BarGlow.Anim:Play()
    end

    -- 播放Starburst动画
    if statusBar.Starburst and statusBar.Starburst.Anim then
        statusBar.Starburst.Anim:Play()
    end

    -- 播放BarFrame2动画
    if statusBar.BarFrame2 and statusBar.BarFrame2.Anim then
        statusBar.BarFrame2.Anim:Play()
    end

    -- 播放BarFrame3动画
    if statusBar.BarFrame3 and statusBar.BarFrame3.Anim then
        statusBar.BarFrame3.Anim:Play()
    end

    -- 播放Sheen动画
    if statusBar.Sheen and statusBar.Sheen.Anim then
        statusBar.Sheen.Anim:Play()
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar所有动画已启动")
end

-- 创建框架用于事件处理
local eventFrame = CreateFrame("Frame")
eventFrame:SetScript("OnEvent", function(_, event, ...)
    ScenarioBlocksFrameManager:OnEvent(event, ...)
end)

-- 注册事件
eventFrame:RegisterEvent("ADDON_LOADED")

-- ========================================
-- 动画播放便捷函数
-- ========================================

-- 播放ObjectiveTrackerCheckLine的Glow动画
-- @param frame Frame ObjectiveTrackerCheckLine框架
function ScenarioBlocksFrameManager:PlayGlowAnimation(frame)
    if frame and frame.Glow and frame.Glow.Anim then
        frame.Glow.Anim:Play()
        DebugPrint("|cff00ff00ScenarioBlocks|r: 播放Glow动画")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放Glow动画 - 框架或动画不存在")
    end
end

-- 播放ObjectiveTrackerCheckLine的CheckFlash动画
-- @param frame Frame ObjectiveTrackerCheckLine框架
function ScenarioBlocksFrameManager:PlayCheckFlashAnimation(frame)
    if frame and frame.CheckFlash and frame.CheckFlash.Anim then
        frame.CheckFlash:Show() -- 显示CheckFlash纹理
        frame.CheckFlash.Anim:Play()
        DebugPrint("|cff00ff00ScenarioBlocks|r: 播放CheckFlash动画")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放CheckFlash动画 - 框架或动画不存在")
    end
end

-- 播放ObjectiveTrackerCheckLine的Sheen动画
-- @param frame Frame ObjectiveTrackerCheckLine框架
function ScenarioBlocksFrameManager:PlaySheenAnimation(frame)
    if frame and frame.Sheen and frame.Sheen.Anim then
        frame.Sheen.Anim:Play()
        DebugPrint("|cff00ff00ScenarioBlocks|r: 播放Sheen动画")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放Sheen动画 - 框架或动画不存在")
    end
end

-- 播放ObjectiveTrackerCheckLine的所有动画（完整的目标完成效果）
-- @param frame Frame ObjectiveTrackerCheckLine框架
function ScenarioBlocksFrameManager:PlayAllCheckLineAnimations(frame)
    if not frame then
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放动画 - 框架不存在")
        return
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 播放所有CheckLine动画效果")

    -- 播放Glow动画
    self:PlayGlowAnimation(frame)

    -- 播放CheckFlash动画
    self:PlayCheckFlashAnimation(frame)

    -- 播放Sheen动画
    self:PlaySheenAnimation(frame)
end